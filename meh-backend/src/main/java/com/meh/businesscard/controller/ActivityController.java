package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.Activity;
import com.meh.businesscard.entity.Invite;
import com.meh.businesscard.service.ActivityService;
import com.meh.businesscard.service.InviteService;
import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.tags.Tag;
// import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 活动控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
// @Tag(name = "活动管理")
@RestController
@RequestMapping("/activities")
public class ActivityController {

    @Autowired
    private ActivityService activityService;

    @Autowired
    private InviteService inviteService;

    /**
     * 获取活动列表
     *
     * @param type 活动类型
     * @param status 活动状态
     * @return 活动列表
     */
        @Operation(summary = "获取活动列表", description = "获取当前可参与的活动列表")
    ////@ApiImplicitParams({
            //@ApiImplicitParam(name = "type", value = "活动类型：1-裂变营销，2-签到活动，3-其他活动", paramType = "query", dataTypeClass = Integer.class),
            //@ApiImplicitParam(name = "status", value = "活动状态：0-未开始，1-进行中，2-已结束", paramType = "query", dataTypeClass = Integer.class)
    //})
    @GetMapping
    @OperationLog(module = "活动管理", operationType = "查询", description = "获取活动列表")
    public Result<List<Activity>> getActivityList(
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Integer status) {
        return activityService.getActivityList(type, status);
    }

    /**
     * 获取活动详情
     *
     * @param id 活动ID
     * @return 活动详情
     */
        @Operation(summary = "获取活动详情", description = "获取指定活动的详细信息")
    @GetMapping("/{id}")
    @OperationLog(module = "活动管理", operationType = "查询", description = "获取活动详情")
    public Result<Activity> getActivityDetail(@PathVariable Long id) {
        return activityService.getActivityDetail(id);
    }

    /**
     * 参与活动
     *
     * @param id 活动ID
     * @return 参与结果
     */
        @Operation(summary = "参与活动", description = "用户参与指定活动")
    @PostMapping("/{id}/join")
    @OperationLog(module = "活动管理", operationType = "参与", description = "参与活动")
    public Result<?> joinActivity(@PathVariable Long id) {
        return activityService.joinActivity(id);
    }

    /**
     * 生成邀请码
     *
     * @param activityId 活动ID
     * @return 邀请码
     */
    @Operation(summary = "生成邀请码", description = "为指定活动生成用户专属邀请码")
    @PostMapping("/invite")
    @OperationLog(module = "活动管理", operationType = "邀请", description = "生成邀请码")
    public Result<String> generateInviteCode(@RequestParam Long activityId) {
        return inviteService.generateInviteCode(activityId);
    }

    /**
     * 使用邀请码
     *
     * @param inviteCode 邀请码
     * @return 使用结果
     */
    @Operation(summary = "使用邀请码", description = "通过邀请码参与活动")
    @PostMapping("/invite/use")
    @OperationLog(module = "活动管理", operationType = "邀请", description = "使用邀请码")
    public Result<?> useInviteCode(@RequestParam String inviteCode) {
        return inviteService.useInviteCode(inviteCode);
    }

    /**
     * 获取我的邀请记录
     *
     * @param activityId 活动ID
     * @return 邀请记录列表
     */
        @Operation(summary = "获取我的邀请记录", description = "获取用户的邀请记录列表")
    @GetMapping("/invite/my")
    @OperationLog(module = "活动管理", operationType = "查询", description = "获取邀请记录")
    public Result<List<Invite>> getMyInviteList(@RequestParam(required = false) Long activityId) {
        return inviteService.getMyInviteList(activityId);
    }

    /**
     * 获取邀请统计
     *
     * @return 邀请统计信息
     */
        @Operation(summary = "获取邀请统计", description = "获取用户的邀请统计数据")
    @GetMapping("/invite/stats")
    @OperationLog(module = "活动管理", operationType = "查询", description = "获取邀请统计")
    public Result<?> getInviteStats() {
        return inviteService.getInviteStats();
    }

    /**
     * 获取推荐名片列表
     *
     * @return 推荐名片列表
     */
        @Operation(summary = "获取推荐名片列表", description = "获取系统推荐的优质名片")
    @GetMapping("/recommend/cards")
    @OperationLog(module = "活动管理", operationType = "查询", description = "获取推荐名片")
    public Result<?> getRecommendCards() {
        return activityService.getRecommendCards();
    }
}
