package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.UserLoginDTO;
import com.meh.businesscard.dto.UserRegisterDTO;
import com.meh.businesscard.service.UserService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Tag(name = "认证管理")
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private UserService userService;

    /**
     * 用户登录
     *
     * @param loginDTO 登录参数
     * @return 登录结果（包含用户信息和JWT令牌）
     */
        @Operation(summary = "用户登录", description = "通过用户名/手机号和密码登录系统，返回JWT令牌")
    @PostMapping("/login")
    @OperationLog(module = "用户认证", operationType = "登录", description = "用户登录系统")
    public Result<?> login(@RequestBody @Validated UserLoginDTO loginDTO) {
        return userService.login(loginDTO);
    }

    /**
     * 用户注册
     *
     * @param registerDTO 注册参数
     * @return 注册结果
     */
        @Operation(summary = "用户注册", description = "注册新用户，需要提供用户名、手机号、密码等信息")
    @PostMapping("/register")
    @OperationLog(module = "用户认证", operationType = "注册", description = "新用户注册")
    public Result<?> register(@RequestBody @Validated UserRegisterDTO registerDTO) {
        return userService.register(registerDTO);
    }

    /**
     * 微信小程序登录
     *
     * @param code 微信授权码
     * @return 登录结果
     */
        @Operation(summary = "微信小程序登录", description = "通过微信小程序授权码登录系统")
    //@ApiImplicitParam(name = "code", value = "微信授权码", required = true, paramType = "query")
    @PostMapping("/wx-login")
    @OperationLog(module = "用户认证", operationType = "登录", description = "微信小程序登录")
    public Result<?> wxLogin(@RequestParam String code) {
        return userService.wxLogin(code);
    }

    /**
     * 绑定微信手机号
     *
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @param sessionKey 会话密钥
     * @return 绑定结果
     */
        @Operation(summary = "绑定微信手机号", description = "通过微信授权获取并绑定用户手机号")
    @PostMapping("/wx-bind-phone")
    @OperationLog(module = "用户认证", operationType = "绑定", description = "绑定微信手机号")
    public Result<?> bindWxPhone(
            @RequestParam String encryptedData,
            @RequestParam String iv,
            @RequestParam String sessionKey) {
        return userService.bindWxPhone(encryptedData, iv, sessionKey);
    }

    /**
     * 退出登录
     *
     * @return 退出结果
     */
        @Operation(summary = "退出登录", description = "用户退出系统，清除登录状态")
    @PostMapping("/logout")
    @OperationLog(module = "用户认证", operationType = "登出", description = "用户退出登录")
    public Result<?> logout() {
        return userService.logout();
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
        @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    //@ApiImplicitParam(name = "refreshToken", value = "刷新令牌", required = true, paramType = "query")
    @PostMapping("/refresh-token")
    @OperationLog(module = "用户认证", operationType = "刷新", description = "刷新访问令牌")
    public Result<?> refreshToken(@RequestParam String refreshToken) {
        return userService.refreshToken(refreshToken);
    }
}
