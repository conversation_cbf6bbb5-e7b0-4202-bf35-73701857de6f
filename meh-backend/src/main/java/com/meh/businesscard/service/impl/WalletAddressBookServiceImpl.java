package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.AddressBookUpdateDTO;
import com.meh.businesscard.dto.LabelCreateDTO;
import com.meh.businesscard.entity.WalletAddressBook;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.WalletAddressBookMapper;
import com.meh.businesscard.service.WalletAddressBookService;
import com.meh.businesscard.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 钱包地址簿服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletAddressBookServiceImpl implements WalletAddressBookService {

    private final WalletAddressBookMapper addressBookMapper;
    private final UserService userService;

    @Override
    @Transactional
    public Result<?> addContact(AddressBookUpdateDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 检查地址是否已存在
            WalletAddressBook existing = addressBookMapper.selectByAddressAndCoinType(
                currentUser.getId(), dto.getWalletAddress(), dto.getCoinType());
            if (existing != null) {
                return Result.failed("该地址已存在于地址簿中");
            }

            // 创建地址簿记录
            WalletAddressBook addressBook = new WalletAddressBook();
            BeanUtils.copyProperties(dto, addressBook);
            addressBook.setUserId(currentUser.getId());
            addressBook.setUseCount(0);
            addressBook.setDeleted(false);
            addressBook.setCreateTime(LocalDateTime.now());
            addressBook.setUpdateTime(LocalDateTime.now());

            addressBookMapper.insert(addressBook);

            return Result.success("联系人添加成功");

        } catch (Exception e) {
            log.error("添加联系人失败: {}", e.getMessage(), e);
            return Result.failed("添加联系人失败");
        }
    }

    @Override
    public Result<?> getAddressBook(String coinType, String category, String keyword, 
                                   Integer pageNum, Integer pageSize) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 分页查询
            Page<WalletAddressBook> page = new Page<>(pageNum, pageSize);
            IPage<WalletAddressBook> addressBookPage = addressBookMapper.selectAddressBookPage(
                page, currentUser.getId(), coinType, category, keyword);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("records", addressBookPage.getRecords());
            result.put("total", addressBookPage.getTotal());
            result.put("current", addressBookPage.getCurrent());
            result.put("size", addressBookPage.getSize());
            result.put("pages", addressBookPage.getPages());

            return Result.success(result);

        } catch (Exception e) {
            log.error("获取地址簿失败: {}", e.getMessage(), e);
            return Result.failed("获取地址簿失败");
        }
    }

    @Override
    @Transactional
    public Result<?> updateContact(Long contactId, AddressBookUpdateDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权修改该联系人");
            }

            // 更新联系人信息
            BeanUtils.copyProperties(dto, addressBook);
            addressBook.setUpdateTime(LocalDateTime.now());

            addressBookMapper.updateById(addressBook);

            return Result.success("联系人更新成功");

        } catch (Exception e) {
            log.error("更新联系人失败: {}", e.getMessage(), e);
            return Result.failed("更新联系人失败");
        }
    }

    @Override
    @Transactional
    public Result<?> deleteContact(Long contactId) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权删除该联系人");
            }

            // 软删除
            addressBook.setDeleted(true);
            addressBook.setUpdateTime(LocalDateTime.now());
            addressBookMapper.updateById(addressBook);

            return Result.success("联系人删除成功");

        } catch (Exception e) {
            log.error("删除联系人失败: {}", e.getMessage(), e);
            return Result.failed("删除联系人失败");
        }
    }

    @Override
    public Result<?> getContactDetail(Long contactId) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人详情
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权访问该联系人");
            }

            return Result.success(addressBook);

        } catch (Exception e) {
            log.error("获取联系人详情失败: {}", e.getMessage(), e);
            return Result.failed("获取联系人详情失败");
        }
    }

    @Override
    public Result<?> getRecentContacts(String coinType) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取最近联系人
            List<WalletAddressBook> recentContacts = addressBookMapper.selectRecentAddresses(
                currentUser.getId(), coinType, 10);

            return Result.success(recentContacts);

        } catch (Exception e) {
            log.error("获取最近联系人失败: {}", e.getMessage(), e);
            return Result.failed("获取最近联系人失败");
        }
    }

    @Override
    public Result<?> getFrequentAddresses(String coinType) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取常用地址
            List<WalletAddressBook> frequentAddresses = addressBookMapper.selectFrequentAddresses(
                currentUser.getId(), coinType);

            return Result.success(frequentAddresses);

        } catch (Exception e) {
            log.error("获取常用地址失败: {}", e.getMessage(), e);
            return Result.failed("获取常用地址失败");
        }
    }

    @Override
    public Result<?> validateAddress(String address, String coinType) {
        try {
            // TODO: 实现地址验证逻辑
            boolean isValid = address != null && address.length() > 20;
            String riskLevel = "LOW";
            
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", isValid);
            result.put("riskLevel", riskLevel);
            result.put("message", isValid ? "地址有效" : "地址无效");
            
            return Result.success(result);

        } catch (Exception e) {
            log.error("验证地址失败: {}", e.getMessage(), e);
            return Result.failed("验证地址失败");
        }
    }

    @Override
    public Result<?> exportAddressBook() {
        try {
            // TODO: 实现地址簿导出逻辑
            Map<String, Object> result = new HashMap<>();
            result.put("downloadUrl", "https://example.com/exports/addressbook.csv");
            result.put("fileName", "addressbook_" + System.currentTimeMillis() + ".csv");
            
            return Result.success(result, "导出任务已创建");

        } catch (Exception e) {
            log.error("导出地址簿失败: {}", e.getMessage(), e);
            return Result.failed("导出地址簿失败");
        }
    }

    @Override
    public Result<?> importAddressBook(MultipartFile file) {
        try {
            // TODO: 实现地址簿导入逻辑
            return Result.success("地址簿导入成功");

        } catch (Exception e) {
            log.error("导入地址簿失败: {}", e.getMessage(), e);
            return Result.failed("导入地址簿失败");
        }
    }

    @Override
    public Result<?> getAddressLabels() {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取所有标签
            List<String> labels = addressBookMapper.selectAllLabels(currentUser.getId());
            
            // 添加默认标签
            Set<String> allLabels = new HashSet<>(labels);
            allLabels.addAll(Arrays.asList("朋友", "同事", "客户", "交易所", "DeFi", "NFT"));
            
            return Result.success(new ArrayList<>(allLabels));

        } catch (Exception e) {
            log.error("获取地址标签失败: {}", e.getMessage(), e);
            return Result.failed("获取地址标签失败");
        }
    }

    @Override
    public Result<?> createAddressLabel(LabelCreateDTO dto) {
        try {
            // TODO: 实现标签创建逻辑（可以存储到单独的标签表）
            return Result.success("标签创建成功");

        } catch (Exception e) {
            log.error("创建地址标签失败: {}", e.getMessage(), e);
            return Result.failed("创建地址标签失败");
        }
    }

    @Override
    @Transactional
    public Result<?> setFrequentAddress(Long contactId, Boolean isFrequent) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权修改该联系人");
            }

            // 更新常用状态
            addressBook.setIsFrequent(isFrequent);
            addressBook.setUpdateTime(LocalDateTime.now());
            addressBookMapper.updateById(addressBook);

            return Result.success(isFrequent ? "已设为常用地址" : "已取消常用地址");

        } catch (Exception e) {
            log.error("设置常用地址失败: {}", e.getMessage(), e);
            return Result.failed("设置常用地址失败");
        }
    }

    @Override
    public Result<?> searchContacts(String keyword, String coinType) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 分页查询（使用默认分页参数）
            Page<WalletAddressBook> page = new Page<>(1, 20);
            IPage<WalletAddressBook> searchResult = addressBookMapper.selectAddressBookPage(
                page, currentUser.getId(), coinType, null, keyword);

            return Result.success(searchResult.getRecords());

        } catch (Exception e) {
            log.error("搜索联系人失败: {}", e.getMessage(), e);
            return Result.failed("搜索联系人失败");
        }
    }
}
