package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.TransferDTO;

/**
 * 钱包交易服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface WalletTransactionService {

    /**
     * 发起转账
     */
    Result<?> transfer(TransferDTO dto);

    /**
     * 批量转账
     */
    Result<?> batchTransfer(TransferDTO dto);

    /**
     * 获取交易历史
     */
    Result<?> getTransactionHistory(Long walletId, String txType, String status, 
                                   String coinType, String startTime, String endTime, 
                                   Integer pageNum, Integer pageSize);

    /**
     * 获取交易详情
     */
    Result<?> getTransactionDetail(Long transactionId);

    /**
     * 获取交易统计
     */
    Result<?> getTransactionStats(Long walletId);

    /**
     * 获取相关交易
     */
    Result<?> getRelatedTransactions(Long transactionId, String address);

    /**
     * 取消交易
     */
    Result<?> cancelTransaction(Long transactionId, String password);

    /**
     * 加速交易
     */
    Result<?> speedUpTransaction(Long transactionId, String gasPrice, String password);

    /**
     * 导出交易记录
     */
    Result<?> exportTransactions(Long walletId, String format, String startTime, String endTime);

    /**
     * 获取Gas费估算
     */
    Result<?> estimateGasFee(String fromAddress, String toAddress, String amount, String coinType);

    /**
     * 验证交易密码
     */
    Result<?> verifyTransactionPassword(String password);
}
